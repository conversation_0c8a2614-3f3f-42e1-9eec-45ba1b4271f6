// backend/src/controllers/transcriptionController.js
const replicateService = require('../services/replicateService');
const audioProcessingService = require('../services/audioProcessingService'); // For FFmpeg audio extraction
// const subtitleGeneratorService = require('../services/subtitleGeneratorService'); // If generating VTT/SRT manually

// Simple in-memory storage for job metadata (replace with database in production)
const jobMetadata = new Map();

exports.startTranscriptionJob = async (req, res) => {
  const { fileUrl, fileId, language, model } = req.body;

  // Support both fileUrl (for external URLs) and fileId (for local files)
  if (!fileUrl && !fileId) {
    return res.status(400).json({ message: 'Either fileUrl or fileId is required to start transcription.' });
  }

  try {
    let job;

    if (fileId) {
      // Check file size and decide approach
      const path = require('path');
      const fs = require('fs');
      const uploadsDir = path.join(__dirname, '../../uploads');
      const localFilePath = path.join(uploadsDir, fileId);

      // Check if file exists
      if (!fs.existsSync(localFilePath)) {
        return res.status(404).json({ message: 'Uploaded file not found.' });
      }

      // Get file size
      const stats = fs.statSync(localFilePath);
      const fileSizeInMB = stats.size / (1024 * 1024);
      const MAX_BASE64_SIZE_MB = 25; // Reduced to 25MB to avoid Replicate's API limits

      console.log(`File size: ${fileSizeInMB.toFixed(2)}MB`);

      if (fileSizeInMB <= MAX_BASE64_SIZE_MB) {
        // Use base64 approach for smaller files (with retry logic)
        console.log(`Using base64 approach for file: ${localFilePath}`);

        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            job = await replicateService.startTranscriptionFromFile(localFilePath, language, model);
            break; // Success, exit retry loop
          } catch (replicateError) {
            retryCount++;
            console.log(`Transcription attempt ${retryCount} failed:`, replicateError.message);

            if (retryCount >= maxRetries) {
              throw replicateError; // Re-throw after max retries
            }

            // Wait before retrying (exponential backoff)
            const waitTime = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
            console.log(`Retrying in ${waitTime/1000} seconds...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
        }
      } else {
        // For larger files, extract audio first to reduce size
        console.log(`File too large for base64 (${fileSizeInMB.toFixed(2)}MB), extracting audio first`);

        const uploadsDir = path.join(__dirname, '../../uploads');
        const audioFileName = `audio_${Date.now()}.mp3`;
        const audioFilePath = path.join(uploadsDir, audioFileName);

        try {
          // Extract audio from video file using a direct approach
          console.log(`Extracting audio from ${localFilePath} to ${audioFilePath}`);

          // Use FFmpeg directly to extract audio with aggressive compression for smaller file size
          const ffmpeg = require('fluent-ffmpeg');
          await new Promise((resolve, reject) => {
            ffmpeg(localFilePath)
              .noVideo()
              .audioCodec('libmp3lame') // Use MP3 for smaller file size
              .audioChannels(1) // Mono audio for speech recognition
              .audioFrequency(16000) // 16kHz sample rate (optimal for Whisper)
              .audioBitrate('32k') // Very low bitrate to ensure small file size (still acceptable for speech)
              .toFormat('mp3') // MP3 format for smaller size
              .on('start', (commandLine) => {
                console.log('FFmpeg audio extraction started: ' + commandLine);
              })
              .on('end', () => {
                console.log(`Audio extraction finished: ${audioFilePath}`);
                resolve();
              })
              .on('error', (err) => {
                console.error(`Error during audio extraction: ${err.message}`);
                reject(err);
              })
              .save(audioFilePath);
          });

          const extractedAudioPath = audioFilePath;

          // Check audio file size
          const audioStats = fs.statSync(extractedAudioPath);
          const audioSizeInMB = audioStats.size / (1024 * 1024);
          console.log(`Audio file size: ${audioSizeInMB.toFixed(2)}MB`);

          // Safety check: Ensure audio file is under Replicate's 100MB limit
          // Base64 encoding increases size by ~33%, so we want raw audio under 75MB
          if (audioSizeInMB > 75) {
            fs.unlinkSync(extractedAudioPath);
            return res.status(500).json({
              message: `Audio file too large (${audioSizeInMB.toFixed(2)}MB). Replicate has a 100MB limit. Please use a shorter video or lower quality.`
            });
          }

          // Use base64 approach with the extracted audio (with retry logic)
          let retryCount = 0;
          const maxRetries = 3;

          while (retryCount < maxRetries) {
            try {
              job = await replicateService.startTranscriptionFromFile(extractedAudioPath, language, model);
              break; // Success, exit retry loop
            } catch (replicateError) {
              retryCount++;
              console.log(`Transcription attempt ${retryCount} failed:`, replicateError.message);

              if (retryCount >= maxRetries) {
                throw replicateError; // Re-throw after max retries
              }

              // Wait before retrying (exponential backoff)
              const waitTime = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
              console.log(`Retrying in ${waitTime/1000} seconds...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
            }
          }

          // Clean up the temporary audio file after successful transcription start
          fs.unlinkSync(extractedAudioPath);
          console.log(`Cleaned up temporary audio file: ${extractedAudioPath}`);

        } catch (audioError) {
          console.error('Error extracting audio:', audioError);
          // Clean up audio file if it exists
          try {
            if (fs.existsSync(audioFilePath)) {
              fs.unlinkSync(audioFilePath);
            }
          } catch (cleanupError) {
            console.error('Error during cleanup:', cleanupError);
          }
          return res.status(500).json({
            message: 'Failed to process large video file. Audio extraction failed.',
            error: audioError.message
          });
        }
      }
    } else {
      // Use URL approach (for external URLs)
      console.log(`Starting transcription using URL: ${fileUrl}`);
      job = await replicateService.startTranscription(fileUrl, language, model);
    }
    if (!job || !job.id) {
        throw new Error("Failed to create transcription job with Replicate.");
    }

    // Store job metadata for later retrieval
    jobMetadata.set(job.id, {
      originalFileUrl: fileUrl || `local:${fileId}`,
      fileId: fileId,
      language: language,
      model: model,
      createdAt: new Date(),
      status: job.status
    });

    res.status(202).json({
        message: 'Transcription job started with Replicate.',
        jobId: job.id,
        status: job.status
    });
  } catch (error) {
    console.error('Error starting transcription job with Replicate:', error);
    res.status(500).json({ message: 'Failed to start transcription job.', error: error.message });
  }
};

exports.getTranscriptionStatus = async (req, res) => {
  const { jobId } = req.params;
  if (!jobId) {
    return res.status(400).json({ message: 'Job ID is required.' });
  }

  try {
    const jobDetails = await replicateService.getTranscriptionStatus(jobId);

    // The Replicate output for Whisper models often includes:
    // jobDetails.output.segments: array of {start, end, text, words: [{start, end, word}]}
    // jobDetails.output.vtt: URL to a VTT file
    // jobDetails.output.srt: URL to an SRT file
    // jobDetails.output.text: Full plain text transcription
    // jobDetails.output.detected_language: Detected language
    // jobDetails.input.audio: The original audio URL passed to Replicate

    // If Replicate has removed the input data, restore it from our metadata
    const metadata = jobMetadata.get(jobId);
    if (metadata && (!jobDetails.input || Object.keys(jobDetails.input).length === 0)) {
      console.log(`Restoring input data for job ${jobId} from local metadata`);
      jobDetails.input = {
        audio_file: metadata.originalFileUrl,
        language: metadata.language,
        model: metadata.model
      };
    }

    res.status(200).json(jobDetails); // Send the full job details from Replicate

  } catch (error) {
    console.error('Error fetching transcription status from Replicate:', error);
    // Replicate API might return specific errors, pass them through if possible
    if (error.response && error.response.data) {
        return res.status(error.response.status || 500).json({
            message: 'Failed to fetch transcription status from Replicate.',
            replicateError: error.response.data
        });
    }
    res.status(500).json({ message: 'Failed to fetch transcription status.', error: error.message });
  }
};
