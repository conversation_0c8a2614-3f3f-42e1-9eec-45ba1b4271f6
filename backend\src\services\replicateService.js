// backend/src/services/replicateService.js
const Replicate = require('replicate');

// Ensure REPLICATE_API_TOKEN is loaded (dotenv should handle this from .env)
if (!process.env.REPLICATE_API_TOKEN) {
  console.error("FATAL ERROR: REPLICATE_API_TOKEN is not defined in the environment variables.");
  // Consider throwing an error or exiting if this is critical for startup
  // throw new Error("REPLICATE_API_TOKEN is not defined.");
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

const fs = require('fs');
const path = require('path');

// Default model version - user MUST replace this in .env or provide it dynamically
const DEFAULT_WHISPER_MODEL_VERSION = "REPLACE_WITH_ACTUAL_WHISPER_MODEL_ON_REPLICATE";
// Example: "openai/whisper:4d50797290df275329f8b422a0aaef5535eb09d590a141599e049950ddfd1002" (a general Whisper model)
// Or a specific WhisperX variant if available on Replicate: "owner/whisperx-model:versionhash"

/**
 * Converts a local file to a base64 data URI for Replicate
 * @param {string} filePath - Path to the local file
 * @returns {string} Data URI (base64 encoded file)
 */
const fileToDataUri = (filePath) => {
  const fileBuffer = fs.readFileSync(filePath);
  const mimeType = getMimeType(filePath);
  const base64 = fileBuffer.toString('base64');
  return `data:${mimeType};base64,${base64}`;
};

/**
 * Get MIME type based on file extension
 * @param {string} filePath - Path to the file
 * @returns {string} MIME type
 */
const getMimeType = (filePath) => {
  const ext = path.extname(filePath).toLowerCase();
  const mimeTypes = {
    '.mp4': 'video/mp4',
    '.mp3': 'audio/mpeg',
    '.wav': 'audio/wav',
    '.m4a': 'audio/mp4',
    '.aac': 'audio/aac',
    '.ogg': 'audio/ogg',
    '.webm': 'video/webm',
    '.mov': 'video/quicktime',
    '.avi': 'video/x-msvideo'
  };
  return mimeTypes[ext] || 'application/octet-stream';
};

/**
 * Starts a transcription job on Replicate.
 * @param {string} audioInput - Either a publicly accessible URL or a data URI (base64) to the audio/video file.
 * @param {string} [language=null] - Language code (e.g., "en", "es"). Null for auto-detect.
 * @param {string} [model='large-v2'] - Whisper model size (e.g., "large-v2", "medium", "base").
 * @returns {Promise<object>} The Replicate prediction object.
 */
exports.startTranscription = async (audioInput, language = null, model = 'large-v2') => {
  const replicateModelVersion = process.env.REPLICATE_WHISPER_MODEL_VERSION || DEFAULT_WHISPER_MODEL_VERSION;

  if (replicateModelVersion === DEFAULT_WHISPER_MODEL_VERSION) {
    const warningMessage = "Warning: REPLICATE_WHISPER_MODEL_VERSION is not configured or is using the default placeholder. Transcription will likely fail. Please set it in your .env file.";
    console.warn(warningMessage);
    // Optionally, throw an error to prevent API calls with a bad model string:
    // throw new Error(warningMessage);
  }

  const inputType = audioInput.startsWith('data:') ? 'base64 data' : 'URL';
  console.log(`Starting Replicate transcription using ${inputType}`);
  console.log(`Using Replicate model version: ${replicateModelVersion}`);

  const input = {
    audio_file: audioInput, // Can be either URL or data URI (base64)

    // Essential parameters for fine-grained segmentation (from Replicate_API_parameters.md)
    temperature: 0.0, // Lower temperature for more consistent, precise transcription
    align_output: true, // Essential for word-level timestamps (WhisperX specific)

    // WhisperX specific parameters for optimal ESL transcription with fine-grained segmentation
    batch_size: 16, // Smaller batch size for more precise segmentation
    vad_onset: 0.500, // Voice Activity Detection onset threshold
    vad_offset: 0.363, // Voice Activity Detection offset threshold
    language_detection_min_prob: 0, // Language detection minimum probability
    language_detection_max_tries: 5, // Language detection maximum tries
    debug: false, // Set to true if you need debugging info

    // Additional parameters for better sentence-level segmentation
    chunk_size: 30, // Smaller chunk size for more granular processing
    return_char_alignments: false, // Focus on word-level alignments
    print_progress: false, // Disable progress printing for cleaner output

    // Optional diarization (speaker separation) - can be enabled for multi-speaker content
    // diarization: false, // Set to true if multiple speakers need to be identified
    // huggingface_access_token: process.env.HF_TOKEN, // Required if diarization is true
    // min_speakers: null, // Minimum number of speakers (if diarization enabled)
    // max_speakers: null, // Maximum number of speakers (if diarization enabled)
  };

  if (language) { // Only add language to input if it's explicitly provided
    input.language = language;
  }

  try {
    const predictionConfig = {
      version: replicateModelVersion, // The Replicate model_owner/model_name:version_hash string
      input: input
    };

    // Only add webhook configuration if webhook URL is provided
    if (process.env.REPLICATE_WEBHOOK_URL) {
      predictionConfig.webhook = process.env.REPLICATE_WEBHOOK_URL;
      predictionConfig.webhook_events_filter = ["completed", "failed"];
    }

    const prediction = await replicate.predictions.create(predictionConfig);
    console.log(`Replicate job created: ${prediction.id}, Status: ${prediction.status}`);
    return prediction;
  } catch (error) {
    console.error('Error creating Replicate prediction:', error.response ? error.response.data : error.message);
    throw error; // Re-throw to be caught by controller
  }
};

/**
 * Fetches the status and output of a Replicate transcription job.
 * @param {string} jobId - The ID of the Replicate prediction.
 * @returns {Promise<object>} The Replicate prediction object with current status and output.
 */
exports.getTranscriptionStatus = async (jobId) => {
  console.log(`Fetching status for Replicate job: ${jobId}`);
  try {
    const prediction = await replicate.predictions.get(jobId);
    return prediction;
  } catch (error) {
    console.error(`Error fetching Replicate job status for ${jobId}:`, error.response ? error.response.data : error.message);
    throw error; // Re-throw
  }
};

/**
 * Starts a transcription job using a local file (converts to base64)
 * @param {string} localFilePath - Path to the local file
 * @param {string} [language=null] - Language code (e.g., "en", "es"). Null for auto-detect.
 * @param {string} [model='large-v2'] - Whisper model size (e.g., "large-v2", "medium", "base").
 * @returns {Promise<object>} The Replicate prediction object.
 */
exports.startTranscriptionFromFile = async (localFilePath, language = null, model = 'large-v2') => {
  try {
    console.log(`Converting local file to base64: ${localFilePath}`);
    const dataUri = fileToDataUri(localFilePath);
    console.log(`File converted to data URI (${dataUri.length} characters) - sending to Replicate`);

    return await exports.startTranscription(dataUri, language, model);
  } catch (error) {
    console.error('Error converting file to base64 or starting transcription:', error);
    throw error;
  }
};
